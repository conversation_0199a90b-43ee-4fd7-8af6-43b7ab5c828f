﻿<?xml version="1.0" encoding="utf-8"?>
<Schema Namespace="MyPetShopModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
  <EntityType Name="CartItem">
    <Key>
      <PropertyRef Name="CartItemId" />
    </Key>
    <Property Name="CartItemId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="CustomerId" Type="int" Nullable="false" />
    <Property Name="ProId" Type="int" Nullable="false" />
    <Property Name="ProName" Type="nvarchar" MaxLength="80" Nullable="false" />
    <Property Name="ListPrice" Type="decimal" Precision="10" Scale="2" Nullable="false" />
    <Property Name="Qty" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="Category">
    <Key>
      <PropertyRef Name="CategoryId" />
    </Key>
    <Property Name="CategoryId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="80" />
    <Property Name="Descn" Type="nvarchar" MaxLength="255" />
  </EntityType>
  <EntityType Name="Customer">
    <Key>
      <PropertyRef Name="CustomerId" />
    </Key>
    <Property Name="CustomerId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="80" Nullable="false" />
    <Property Name="Password" Type="nvarchar" MaxLength="80" Nullable="false" />
    <Property Name="Email" Type="nvarchar" MaxLength="80" Nullable="false" />
  </EntityType>
  <EntityType Name="Order">
    <Key>
      <PropertyRef Name="OrderId" />
    </Key>
    <Property Name="OrderId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="CustomerId" Type="int" Nullable="false" />
    <Property Name="UserName" Type="nvarchar" MaxLength="80" Nullable="false" />
    <Property Name="OrderDate" Type="datetime" Nullable="false" />
    <Property Name="Addr1" Type="nvarchar" MaxLength="80" />
    <Property Name="Addr2" Type="nvarchar" MaxLength="80" />
    <Property Name="City" Type="nvarchar" MaxLength="80" />
    <Property Name="State" Type="nvarchar" MaxLength="80" />
    <Property Name="Zip" Type="nvarchar" MaxLength="6" />
    <Property Name="Phone" Type="nvarchar" MaxLength="40" />
    <Property Name="Status" Type="nvarchar" MaxLength="10" />
  </EntityType>
  <EntityType Name="OrderItem">
    <Key>
      <PropertyRef Name="ItemId" />
    </Key>
    <Property Name="ItemId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="OrderId" Type="int" Nullable="false" />
    <Property Name="ProName" Type="nvarchar" MaxLength="80" />
    <Property Name="ListPrice" Type="decimal" Precision="10" Scale="2" />
    <Property Name="Qty" Type="int" Nullable="false" />
    <Property Name="TotalPrice" Type="decimal" Precision="10" Scale="2" />
  </EntityType>
  <EntityType Name="Product">
    <Key>
      <PropertyRef Name="ProductId" />
    </Key>
    <Property Name="ProductId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="CategoryId" Type="int" Nullable="false" />
    <Property Name="ListPrice" Type="decimal" Precision="10" Scale="2" />
    <Property Name="UnitCost" Type="decimal" Precision="10" Scale="2" />
    <Property Name="SuppId" Type="int" />
    <Property Name="Name" Type="nvarchar" MaxLength="80" />
    <Property Name="Descn" Type="nvarchar" MaxLength="255" />
    <Property Name="Image" Type="nvarchar" MaxLength="80" />
    <Property Name="Qty" Type="int" Nullable="false" />
  </EntityType>
  <EntityType Name="Supplier">
    <Key>
      <PropertyRef Name="SuppId" />
    </Key>
    <Property Name="SuppId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
    <Property Name="Name" Type="nvarchar" MaxLength="80" />
    <Property Name="Addr1" Type="nvarchar" MaxLength="80" />
    <Property Name="Addr2" Type="nvarchar" MaxLength="80" />
    <Property Name="City" Type="nvarchar" MaxLength="80" />
    <Property Name="State" Type="nvarchar" MaxLength="80" />
    <Property Name="Zip" Type="nvarchar" MaxLength="6" />
    <Property Name="Phone" Type="nvarchar" MaxLength="40" />
  </EntityType>
  <Association Name="FK__CartItem__Custom__32E0915F">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="CustomerId" />
      </Principal>
      <Dependent Role="CartItem">
        <PropertyRef Name="CustomerId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__CartItem__ProId__33D4B598">
    <End Role="Product" Type="Self.Product" Multiplicity="1" />
    <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Product">
        <PropertyRef Name="ProductId" />
      </Principal>
      <Dependent Role="CartItem">
        <PropertyRef Name="ProId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__Order__CustomerI__276EDEB3">
    <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
    <End Role="Order" Type="Self.Order" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Customer">
        <PropertyRef Name="CustomerId" />
      </Principal>
      <Dependent Role="Order">
        <PropertyRef Name="CustomerId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__OrderItem__Order__2A4B4B5E">
    <End Role="Order" Type="Self.Order" Multiplicity="1" />
    <End Role="OrderItem" Type="Self.OrderItem" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Order">
        <PropertyRef Name="OrderId" />
      </Principal>
      <Dependent Role="OrderItem">
        <PropertyRef Name="OrderId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__Product__Categor__2F10007B">
    <End Role="Category" Type="Self.Category" Multiplicity="1" />
    <End Role="Product" Type="Self.Product" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Category">
        <PropertyRef Name="CategoryId" />
      </Principal>
      <Dependent Role="Product">
        <PropertyRef Name="CategoryId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <Association Name="FK__Product__SuppId__300424B4">
    <End Role="Supplier" Type="Self.Supplier" Multiplicity="0..1" />
    <End Role="Product" Type="Self.Product" Multiplicity="*" />
    <ReferentialConstraint>
      <Principal Role="Supplier">
        <PropertyRef Name="SuppId" />
      </Principal>
      <Dependent Role="Product">
        <PropertyRef Name="SuppId" />
      </Dependent>
    </ReferentialConstraint>
  </Association>
  <EntityContainer Name="MyPetShopModelStoreContainer">
    <EntitySet Name="CartItem" EntityType="Self.CartItem" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Category" EntityType="Self.Category" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Customer" EntityType="Self.Customer" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Order" EntityType="Self.Order" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="OrderItem" EntityType="Self.OrderItem" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Product" EntityType="Self.Product" Schema="dbo" store:Type="Tables" />
    <EntitySet Name="Supplier" EntityType="Self.Supplier" Schema="dbo" store:Type="Tables" />
    <AssociationSet Name="FK__CartItem__Custom__32E0915F" Association="Self.FK__CartItem__Custom__32E0915F">
      <End Role="Customer" EntitySet="Customer" />
      <End Role="CartItem" EntitySet="CartItem" />
    </AssociationSet>
    <AssociationSet Name="FK__CartItem__ProId__33D4B598" Association="Self.FK__CartItem__ProId__33D4B598">
      <End Role="Product" EntitySet="Product" />
      <End Role="CartItem" EntitySet="CartItem" />
    </AssociationSet>
    <AssociationSet Name="FK__Order__CustomerI__276EDEB3" Association="Self.FK__Order__CustomerI__276EDEB3">
      <End Role="Customer" EntitySet="Customer" />
      <End Role="Order" EntitySet="Order" />
    </AssociationSet>
    <AssociationSet Name="FK__OrderItem__Order__2A4B4B5E" Association="Self.FK__OrderItem__Order__2A4B4B5E">
      <End Role="Order" EntitySet="Order" />
      <End Role="OrderItem" EntitySet="OrderItem" />
    </AssociationSet>
    <AssociationSet Name="FK__Product__Categor__2F10007B" Association="Self.FK__Product__Categor__2F10007B">
      <End Role="Category" EntitySet="Category" />
      <End Role="Product" EntitySet="Product" />
    </AssociationSet>
    <AssociationSet Name="FK__Product__SuppId__300424B4" Association="Self.FK__Product__SuppId__300424B4">
      <End Role="Supplier" EntitySet="Supplier" />
      <End Role="Product" EntitySet="Product" />
    </AssociationSet>
  </EntityContainer>
</Schema>