﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Site1.master.cs" Inherits="WebApplication1.Site1" %>

<!DOCTYPE html>

<html>
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>
  
    <style type="text/css">
        .auto-style1 {
            width: 400px;
            background-color: #00FFFF;
        }
        .auto-style2 {
            width: 121px;
            height: 295px;
        }
        .auto-style3 {
            height: 295px;
        }
        .auto-style4 {
            height: 69px;
            background-color:aquamarine;
        }
    </style>
  
</head>
<body>
    <form id="form1" runat="server">
        <div>
        
            <table class="auto-style1">
                <tr>
                    <td class="auto-style4" colspan="2">xxxxxxLOGO<br />
                        <br />
                        <asp:HyperLink ID="HyperLink2" runat="server" NavigateUrl="~/index.aspx">首页</asp:HyperLink>
                        &nbsp;&nbsp;&nbsp; page3</td>
                </tr>
                <tr>
                    <td class="auto-style2" valign="top">left<br />
                        <a href="page1.aspx">page1</a><br />
                        <br />
                        <asp:HyperLink ID="HyperLink1" runat="server" NavigateUrl="~/page2.aspx">page2</asp:HyperLink>
                    </td>
                    <td class="auto-style3"  valign="top">
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </td>
                </tr>
            </table>
        
        </div>
    </form>
</body>
</html>
