﻿<?xml version="1.0" encoding="utf-8" ?>
<!-- Copyright (c)  Microsoft.  All Rights Reserved.  Licensed under the Apache License, Version 2.0.  See License.txt in the project root for license information. -->

<configuration>
  <runtime>
    <gcServer enabled="true" />
    <gcConcurrent enabled="false"/>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.CodeAnalysis" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="1.0.0.0-1.3.1.0" newVersion="1.3.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.CodeAnalysis.CSharp" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="1.0.0.0-1.3.1.0" newVersion="1.3.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.CodeAnalysis.VisualBasic" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="1.0.0.0-1.3.1.0" newVersion="1.3.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" culture="neutral" publicKeyToken="b03f5f7f11d50a3a" />
        <bindingRedirect oldVersion="1.1.0.0-1.1.65535.65535" newVersion="1.1.37.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Reflection.Metadata" culture="neutral" publicKeyToken="b03f5f7f11d50a3a" />
        <bindingRedirect oldVersion="1.0.0.0-1.1.65535.65535" newVersion="1.2.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
