﻿<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
  <!-- EF Runtime content -->
  <edmx:Runtime>
    <!-- SSDL content -->
    <edmx:StorageModels>
      <Schema Namespace="MyPetShopModel.Store" Provider="System.Data.SqlClient" ProviderManifestToken="2012" Alias="Self" xmlns:store="http://schemas.microsoft.com/ado/2007/12/edm/EntityStoreSchemaGenerator" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm/ssdl">
        <EntityType Name="CartItem">
          <Key>
            <PropertyRef Name="CartItemId" />
          </Key>
          <Property Name="CartItemId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CustomerId" Type="int" Nullable="false" />
          <Property Name="ProId" Type="int" Nullable="false" />
          <Property Name="ProName" Type="nvarchar" MaxLength="80" Nullable="false" />
          <Property Name="ListPrice" Type="decimal" Precision="10" Scale="2" Nullable="false" />
          <Property Name="Qty" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="Category">
          <Key>
            <PropertyRef Name="CategoryId" />
          </Key>
          <Property Name="CategoryId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="80" />
          <Property Name="Descn" Type="nvarchar" MaxLength="255" />
        </EntityType>
        <EntityType Name="Customer">
          <Key>
            <PropertyRef Name="CustomerId" />
          </Key>
          <Property Name="CustomerId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="80" Nullable="false" />
          <Property Name="Password" Type="nvarchar" MaxLength="80" Nullable="false" />
          <Property Name="Email" Type="nvarchar" MaxLength="80" Nullable="false" />
        </EntityType>
        <EntityType Name="Order">
          <Key>
            <PropertyRef Name="OrderId" />
          </Key>
          <Property Name="OrderId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CustomerId" Type="int" Nullable="false" />
          <Property Name="UserName" Type="nvarchar" MaxLength="80" Nullable="false" />
          <Property Name="OrderDate" Type="datetime" Nullable="false" />
          <Property Name="Addr1" Type="nvarchar" MaxLength="80" />
          <Property Name="Addr2" Type="nvarchar" MaxLength="80" />
          <Property Name="City" Type="nvarchar" MaxLength="80" />
          <Property Name="State" Type="nvarchar" MaxLength="80" />
          <Property Name="Zip" Type="nvarchar" MaxLength="6" />
          <Property Name="Phone" Type="nvarchar" MaxLength="40" />
          <Property Name="Status" Type="nvarchar" MaxLength="10" />
        </EntityType>
        <EntityType Name="OrderItem">
          <Key>
            <PropertyRef Name="ItemId" />
          </Key>
          <Property Name="ItemId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="OrderId" Type="int" Nullable="false" />
          <Property Name="ProName" Type="nvarchar" MaxLength="80" />
          <Property Name="ListPrice" Type="decimal" Precision="10" Scale="2" />
          <Property Name="Qty" Type="int" Nullable="false" />
          <Property Name="TotalPrice" Type="decimal" Precision="10" Scale="2" />
        </EntityType>
        <EntityType Name="Product">
          <Key>
            <PropertyRef Name="ProductId" />
          </Key>
          <Property Name="ProductId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="CategoryId" Type="int" Nullable="false" />
          <Property Name="ListPrice" Type="decimal" Precision="10" Scale="2" />
          <Property Name="UnitCost" Type="decimal" Precision="10" Scale="2" />
          <Property Name="SuppId" Type="int" />
          <Property Name="Name" Type="nvarchar" MaxLength="80" />
          <Property Name="Descn" Type="nvarchar" MaxLength="255" />
          <Property Name="Image" Type="nvarchar" MaxLength="80" />
          <Property Name="Qty" Type="int" Nullable="false" />
        </EntityType>
        <EntityType Name="Supplier">
          <Key>
            <PropertyRef Name="SuppId" />
          </Key>
          <Property Name="SuppId" Type="int" StoreGeneratedPattern="Identity" Nullable="false" />
          <Property Name="Name" Type="nvarchar" MaxLength="80" />
          <Property Name="Addr1" Type="nvarchar" MaxLength="80" />
          <Property Name="Addr2" Type="nvarchar" MaxLength="80" />
          <Property Name="City" Type="nvarchar" MaxLength="80" />
          <Property Name="State" Type="nvarchar" MaxLength="80" />
          <Property Name="Zip" Type="nvarchar" MaxLength="6" />
          <Property Name="Phone" Type="nvarchar" MaxLength="40" />
        </EntityType>
        <Association Name="FK__CartItem__Custom__32E0915F">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="CustomerId" />
            </Principal>
            <Dependent Role="CartItem">
              <PropertyRef Name="CustomerId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__CartItem__ProId__33D4B598">
          <End Role="Product" Type="Self.Product" Multiplicity="1" />
          <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Product">
              <PropertyRef Name="ProductId" />
            </Principal>
            <Dependent Role="CartItem">
              <PropertyRef Name="ProId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__Order__CustomerI__276EDEB3">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="Order" Type="Self.Order" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="CustomerId" />
            </Principal>
            <Dependent Role="Order">
              <PropertyRef Name="CustomerId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__OrderItem__Order__2A4B4B5E">
          <End Role="Order" Type="Self.Order" Multiplicity="1" />
          <End Role="OrderItem" Type="Self.OrderItem" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Order">
              <PropertyRef Name="OrderId" />
            </Principal>
            <Dependent Role="OrderItem">
              <PropertyRef Name="OrderId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__Product__Categor__2F10007B">
          <End Role="Category" Type="Self.Category" Multiplicity="1" />
          <End Role="Product" Type="Self.Product" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Category">
              <PropertyRef Name="CategoryId" />
            </Principal>
            <Dependent Role="Product">
              <PropertyRef Name="CategoryId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__Product__SuppId__300424B4">
          <End Role="Supplier" Type="Self.Supplier" Multiplicity="0..1" />
          <End Role="Product" Type="Self.Product" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Supplier">
              <PropertyRef Name="SuppId" />
            </Principal>
            <Dependent Role="Product">
              <PropertyRef Name="SuppId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="MyPetShopModelStoreContainer">
          <EntitySet Name="CartItem" EntityType="Self.CartItem" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Category" EntityType="Self.Category" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Customer" EntityType="Self.Customer" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Order" EntityType="Self.Order" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="OrderItem" EntityType="Self.OrderItem" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Product" EntityType="Self.Product" Schema="dbo" store:Type="Tables" />
          <EntitySet Name="Supplier" EntityType="Self.Supplier" Schema="dbo" store:Type="Tables" />
          <AssociationSet Name="FK__CartItem__Custom__32E0915F" Association="Self.FK__CartItem__Custom__32E0915F">
            <End Role="Customer" EntitySet="Customer" />
            <End Role="CartItem" EntitySet="CartItem" />
          </AssociationSet>
          <AssociationSet Name="FK__CartItem__ProId__33D4B598" Association="Self.FK__CartItem__ProId__33D4B598">
            <End Role="Product" EntitySet="Product" />
            <End Role="CartItem" EntitySet="CartItem" />
          </AssociationSet>
          <AssociationSet Name="FK__Order__CustomerI__276EDEB3" Association="Self.FK__Order__CustomerI__276EDEB3">
            <End Role="Customer" EntitySet="Customer" />
            <End Role="Order" EntitySet="Order" />
          </AssociationSet>
          <AssociationSet Name="FK__OrderItem__Order__2A4B4B5E" Association="Self.FK__OrderItem__Order__2A4B4B5E">
            <End Role="Order" EntitySet="Order" />
            <End Role="OrderItem" EntitySet="OrderItem" />
          </AssociationSet>
          <AssociationSet Name="FK__Product__Categor__2F10007B" Association="Self.FK__Product__Categor__2F10007B">
            <End Role="Category" EntitySet="Category" />
            <End Role="Product" EntitySet="Product" />
          </AssociationSet>
          <AssociationSet Name="FK__Product__SuppId__300424B4" Association="Self.FK__Product__SuppId__300424B4">
            <End Role="Supplier" EntitySet="Supplier" />
            <End Role="Product" EntitySet="Product" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:StorageModels>
    <!-- CSDL content -->
    <edmx:ConceptualModels>
      <Schema Namespace="MyPetShopModel" Alias="Self" annotation:UseStrongSpatialTypes="false" xmlns:annotation="http://schemas.microsoft.com/ado/2009/02/edm/annotation" xmlns:customannotation="http://schemas.microsoft.com/ado/2013/11/edm/customannotation" xmlns="http://schemas.microsoft.com/ado/2009/11/edm">
        <EntityType Name="CartItem">
          <Key>
            <PropertyRef Name="CartItemId" />
          </Key>
          <Property Name="CartItemId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CustomerId" Type="Int32" Nullable="false" />
          <Property Name="ProId" Type="Int32" Nullable="false" />
          <Property Name="ProName" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="ListPrice" Type="Decimal" Precision="10" Scale="2" Nullable="false" />
          <Property Name="Qty" Type="Int32" Nullable="false" />
          <NavigationProperty Name="Customer" Relationship="Self.FK__CartItem__Custom__32E0915F" FromRole="CartItem" ToRole="Customer" />
          <NavigationProperty Name="Product" Relationship="Self.FK__CartItem__ProId__33D4B598" FromRole="CartItem" ToRole="Product" />
        </EntityType>
        <EntityType Name="Category">
          <Key>
            <PropertyRef Name="CategoryId" />
          </Key>
          <Property Name="CategoryId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Descn" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Product" Relationship="Self.FK__Product__Categor__2F10007B" FromRole="Category" ToRole="Product" />
        </EntityType>
        <EntityType Name="Customer">
          <Key>
            <PropertyRef Name="CustomerId" />
          </Key>
          <Property Name="CustomerId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Password" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="Email" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
          <NavigationProperty Name="CartItem" Relationship="Self.FK__CartItem__Custom__32E0915F" FromRole="Customer" ToRole="CartItem" />
          <NavigationProperty Name="Order" Relationship="Self.FK__Order__CustomerI__276EDEB3" FromRole="Customer" ToRole="Order" />
        </EntityType>
        <EntityType Name="Order">
          <Key>
            <PropertyRef Name="OrderId" />
          </Key>
          <Property Name="OrderId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CustomerId" Type="Int32" Nullable="false" />
          <Property Name="UserName" Type="String" MaxLength="80" FixedLength="false" Unicode="true" Nullable="false" />
          <Property Name="OrderDate" Type="DateTime" Nullable="false" Precision="3" />
          <Property Name="Addr1" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Addr2" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="City" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="State" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Zip" Type="String" MaxLength="6" FixedLength="false" Unicode="true" />
          <Property Name="Phone" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <Property Name="Status" Type="String" MaxLength="10" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Customer" Relationship="Self.FK__Order__CustomerI__276EDEB3" FromRole="Order" ToRole="Customer" />
          <NavigationProperty Name="OrderItem" Relationship="Self.FK__OrderItem__Order__2A4B4B5E" FromRole="Order" ToRole="OrderItem" />
        </EntityType>
        <EntityType Name="OrderItem">
          <Key>
            <PropertyRef Name="ItemId" />
          </Key>
          <Property Name="ItemId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="OrderId" Type="Int32" Nullable="false" />
          <Property Name="ProName" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="ListPrice" Type="Decimal" Precision="10" Scale="2" />
          <Property Name="Qty" Type="Int32" Nullable="false" />
          <Property Name="TotalPrice" Type="Decimal" Precision="10" Scale="2" />
          <NavigationProperty Name="Order" Relationship="Self.FK__OrderItem__Order__2A4B4B5E" FromRole="OrderItem" ToRole="Order" />
        </EntityType>
        <EntityType Name="Product">
          <Key>
            <PropertyRef Name="ProductId" />
          </Key>
          <Property Name="ProductId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="CategoryId" Type="Int32" Nullable="false" />
          <Property Name="ListPrice" Type="Decimal" Precision="10" Scale="2" />
          <Property Name="UnitCost" Type="Decimal" Precision="10" Scale="2" />
          <Property Name="SuppId" Type="Int32" />
          <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Descn" Type="String" MaxLength="255" FixedLength="false" Unicode="true" />
          <Property Name="Image" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Qty" Type="Int32" Nullable="false" />
          <NavigationProperty Name="CartItem" Relationship="Self.FK__CartItem__ProId__33D4B598" FromRole="Product" ToRole="CartItem" />
          <NavigationProperty Name="Category" Relationship="Self.FK__Product__Categor__2F10007B" FromRole="Product" ToRole="Category" />
          <NavigationProperty Name="Supplier" Relationship="Self.FK__Product__SuppId__300424B4" FromRole="Product" ToRole="Supplier" />
        </EntityType>
        <EntityType Name="Supplier">
          <Key>
            <PropertyRef Name="SuppId" />
          </Key>
          <Property Name="SuppId" Type="Int32" Nullable="false" annotation:StoreGeneratedPattern="Identity" />
          <Property Name="Name" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Addr1" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Addr2" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="City" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="State" Type="String" MaxLength="80" FixedLength="false" Unicode="true" />
          <Property Name="Zip" Type="String" MaxLength="6" FixedLength="false" Unicode="true" />
          <Property Name="Phone" Type="String" MaxLength="40" FixedLength="false" Unicode="true" />
          <NavigationProperty Name="Product" Relationship="Self.FK__Product__SuppId__300424B4" FromRole="Supplier" ToRole="Product" />
        </EntityType>
        <Association Name="FK__CartItem__Custom__32E0915F">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="CustomerId" />
            </Principal>
            <Dependent Role="CartItem">
              <PropertyRef Name="CustomerId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__CartItem__ProId__33D4B598">
          <End Role="Product" Type="Self.Product" Multiplicity="1" />
          <End Role="CartItem" Type="Self.CartItem" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Product">
              <PropertyRef Name="ProductId" />
            </Principal>
            <Dependent Role="CartItem">
              <PropertyRef Name="ProId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__Product__Categor__2F10007B">
          <End Role="Category" Type="Self.Category" Multiplicity="1" />
          <End Role="Product" Type="Self.Product" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Category">
              <PropertyRef Name="CategoryId" />
            </Principal>
            <Dependent Role="Product">
              <PropertyRef Name="CategoryId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__Order__CustomerI__276EDEB3">
          <End Role="Customer" Type="Self.Customer" Multiplicity="1" />
          <End Role="Order" Type="Self.Order" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Customer">
              <PropertyRef Name="CustomerId" />
            </Principal>
            <Dependent Role="Order">
              <PropertyRef Name="CustomerId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__OrderItem__Order__2A4B4B5E">
          <End Role="Order" Type="Self.Order" Multiplicity="1" />
          <End Role="OrderItem" Type="Self.OrderItem" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Order">
              <PropertyRef Name="OrderId" />
            </Principal>
            <Dependent Role="OrderItem">
              <PropertyRef Name="OrderId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <Association Name="FK__Product__SuppId__300424B4">
          <End Role="Supplier" Type="Self.Supplier" Multiplicity="0..1" />
          <End Role="Product" Type="Self.Product" Multiplicity="*" />
          <ReferentialConstraint>
            <Principal Role="Supplier">
              <PropertyRef Name="SuppId" />
            </Principal>
            <Dependent Role="Product">
              <PropertyRef Name="SuppId" />
            </Dependent>
          </ReferentialConstraint>
        </Association>
        <EntityContainer Name="MyPetShopEntities" annotation:LazyLoadingEnabled="true">
          <EntitySet Name="CartItem" EntityType="Self.CartItem" />
          <EntitySet Name="Category" EntityType="Self.Category" />
          <EntitySet Name="Customer" EntityType="Self.Customer" />
          <EntitySet Name="Order" EntityType="Self.Order" />
          <EntitySet Name="OrderItem" EntityType="Self.OrderItem" />
          <EntitySet Name="Product" EntityType="Self.Product" />
          <EntitySet Name="Supplier" EntityType="Self.Supplier" />
          <AssociationSet Name="FK__CartItem__Custom__32E0915F" Association="Self.FK__CartItem__Custom__32E0915F">
            <End Role="Customer" EntitySet="Customer" />
            <End Role="CartItem" EntitySet="CartItem" />
          </AssociationSet>
          <AssociationSet Name="FK__CartItem__ProId__33D4B598" Association="Self.FK__CartItem__ProId__33D4B598">
            <End Role="Product" EntitySet="Product" />
            <End Role="CartItem" EntitySet="CartItem" />
          </AssociationSet>
          <AssociationSet Name="FK__Product__Categor__2F10007B" Association="Self.FK__Product__Categor__2F10007B">
            <End Role="Category" EntitySet="Category" />
            <End Role="Product" EntitySet="Product" />
          </AssociationSet>
          <AssociationSet Name="FK__Order__CustomerI__276EDEB3" Association="Self.FK__Order__CustomerI__276EDEB3">
            <End Role="Customer" EntitySet="Customer" />
            <End Role="Order" EntitySet="Order" />
          </AssociationSet>
          <AssociationSet Name="FK__OrderItem__Order__2A4B4B5E" Association="Self.FK__OrderItem__Order__2A4B4B5E">
            <End Role="Order" EntitySet="Order" />
            <End Role="OrderItem" EntitySet="OrderItem" />
          </AssociationSet>
          <AssociationSet Name="FK__Product__SuppId__300424B4" Association="Self.FK__Product__SuppId__300424B4">
            <End Role="Supplier" EntitySet="Supplier" />
            <End Role="Product" EntitySet="Product" />
          </AssociationSet>
        </EntityContainer>
      </Schema>
    </edmx:ConceptualModels>
    <!-- C-S mapping content -->
    <edmx:Mappings>
      <Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
        <EntityContainerMapping StorageEntityContainer="MyPetShopModelStoreContainer" CdmEntityContainer="MyPetShopEntities">
          <EntitySetMapping Name="CartItem">
            <EntityTypeMapping TypeName="MyPetShopModel.CartItem">
              <MappingFragment StoreEntitySet="CartItem">
                <ScalarProperty Name="CartItemId" ColumnName="CartItemId" />
                <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
                <ScalarProperty Name="ProId" ColumnName="ProId" />
                <ScalarProperty Name="ProName" ColumnName="ProName" />
                <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
                <ScalarProperty Name="Qty" ColumnName="Qty" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Category">
            <EntityTypeMapping TypeName="MyPetShopModel.Category">
              <MappingFragment StoreEntitySet="Category">
                <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Descn" ColumnName="Descn" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Customer">
            <EntityTypeMapping TypeName="MyPetShopModel.Customer">
              <MappingFragment StoreEntitySet="Customer">
                <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Password" ColumnName="Password" />
                <ScalarProperty Name="Email" ColumnName="Email" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Order">
            <EntityTypeMapping TypeName="MyPetShopModel.Order">
              <MappingFragment StoreEntitySet="Order">
                <ScalarProperty Name="OrderId" ColumnName="OrderId" />
                <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
                <ScalarProperty Name="UserName" ColumnName="UserName" />
                <ScalarProperty Name="OrderDate" ColumnName="OrderDate" />
                <ScalarProperty Name="Addr1" ColumnName="Addr1" />
                <ScalarProperty Name="Addr2" ColumnName="Addr2" />
                <ScalarProperty Name="City" ColumnName="City" />
                <ScalarProperty Name="State" ColumnName="State" />
                <ScalarProperty Name="Zip" ColumnName="Zip" />
                <ScalarProperty Name="Phone" ColumnName="Phone" />
                <ScalarProperty Name="Status" ColumnName="Status" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="OrderItem">
            <EntityTypeMapping TypeName="MyPetShopModel.OrderItem">
              <MappingFragment StoreEntitySet="OrderItem">
                <ScalarProperty Name="ItemId" ColumnName="ItemId" />
                <ScalarProperty Name="OrderId" ColumnName="OrderId" />
                <ScalarProperty Name="ProName" ColumnName="ProName" />
                <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
                <ScalarProperty Name="Qty" ColumnName="Qty" />
                <ScalarProperty Name="TotalPrice" ColumnName="TotalPrice" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Product">
            <EntityTypeMapping TypeName="MyPetShopModel.Product">
              <MappingFragment StoreEntitySet="Product">
                <ScalarProperty Name="ProductId" ColumnName="ProductId" />
                <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
                <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
                <ScalarProperty Name="UnitCost" ColumnName="UnitCost" />
                <ScalarProperty Name="SuppId" ColumnName="SuppId" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Descn" ColumnName="Descn" />
                <ScalarProperty Name="Image" ColumnName="Image" />
                <ScalarProperty Name="Qty" ColumnName="Qty" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
          <EntitySetMapping Name="Supplier">
            <EntityTypeMapping TypeName="MyPetShopModel.Supplier">
              <MappingFragment StoreEntitySet="Supplier">
                <ScalarProperty Name="SuppId" ColumnName="SuppId" />
                <ScalarProperty Name="Name" ColumnName="Name" />
                <ScalarProperty Name="Addr1" ColumnName="Addr1" />
                <ScalarProperty Name="Addr2" ColumnName="Addr2" />
                <ScalarProperty Name="City" ColumnName="City" />
                <ScalarProperty Name="State" ColumnName="State" />
                <ScalarProperty Name="Zip" ColumnName="Zip" />
                <ScalarProperty Name="Phone" ColumnName="Phone" />
              </MappingFragment>
            </EntityTypeMapping>
          </EntitySetMapping>
        </EntityContainerMapping>
      </Mapping>
    </edmx:Mappings>
  </edmx:Runtime>
  <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <Connection>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="MetadataArtifactProcessing" Value="EmbedInOutputAssembly" />
      </DesignerInfoPropertySet>
    </Connection>
    <Options>
      <DesignerInfoPropertySet>
        <DesignerProperty Name="ValidateOnBuild" Value="true" />
        <DesignerProperty Name="EnablePluralization" Value="false" />
        <DesignerProperty Name="IncludeForeignKeysInModel" Value="true" />
        <DesignerProperty Name="UseLegacyProvider" Value="false" />
        <DesignerProperty Name="CodeGenerationStrategy" Value="无" />
      </DesignerInfoPropertySet>
    </Options>
    <!-- Diagram content (shape and connector positions) -->
    <Diagrams></Diagrams>
  </Designer>
</edmx:Edmx>