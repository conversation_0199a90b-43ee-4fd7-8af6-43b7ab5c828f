﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebApplication1
{
    public partial class productList : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            binddata();
        }

        public void binddata()
        {
            this.GridView2.DataSource = db.Product.ToList();
            this.GridView2.DataBind();
        }

        protected void GridView2_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
            string pid = this.GridView2.Rows[e.RowIndex].Cells[0].Text;

            Product p=  db.Product.Find(int.Parse(pid));
            db.Product.Remove(p);
            db.SaveChanges();
            binddata();

        }

        protected void GridView2_RowEditing(object sender, GridViewEditEventArgs e)
        {
           // e.NewEditIndex;
        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            
        }

        protected void GridView2_PageIndexChanging(object sender, GridViewPageEventArgs e)
        {
            this.GridView2.PageIndex = e.NewPageIndex;
            this.GridView2.DataBind();
        }
    }
}