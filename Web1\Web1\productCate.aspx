﻿<%@ Page Title="" Language="C#" MasterPageFile="~/q.Master" AutoEventWireup="true" CodeBehind="productCate.aspx.cs" Inherits="Web1.productCate" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:DataList ID="DataList2" runat="server" BackColor="LightGoldenrodYellow" BorderColor="Tan" BorderWidth="1px" CellPadding="2" ForeColor="Black" RepeatColumns="4">
        <AlternatingItemStyle BackColor="PaleGoldenrod" />
        <FooterStyle BackColor="Tan" />
        <HeaderStyle BackColor="Tan" Font-Bold="True" />
        <ItemTemplate>
            <table class="auto-style2">
                <tr>
                    <td style="text-align: center">
                        <asp:Image ID="Image1" runat="server" Height="88px" Width="73px" ImageUrl='<%# Eval("Image") %>' />
                    </td>
                </tr>
                <tr>
                    <td style="text-align: center">
                        <asp:HyperLink ID="HyperLink9" runat="server" NavigateUrl='<%# "productDetail.aspx?pid="+Eval("ProductId") %>' Text='<%# Eval("Name") %>'></asp:HyperLink>
                    </td>
                </tr>
       
            </table>
        </ItemTemplate>
        <SelectedItemStyle BackColor="DarkSlateBlue" ForeColor="GhostWhite" />
    </asp:DataList>
</asp:Content>
