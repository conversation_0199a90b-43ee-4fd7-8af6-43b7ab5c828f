﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productDetail : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string pid = Request.QueryString["pid"].ToString();

                Product p = db.Product.Find(int.Parse(pid));

                this.Label2.Text = p.ProductId.ToString();
                this.Label3.Text = p.ListPrice.ToString();
                this.Label4.Text = p.Name;
                this.Label5.Text = p.Descn;
                this.Label6.Text = p.Qty.ToString();

                this.Image1.ImageUrl = p.Image;

            }
        }
    }
}