﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productAdd2 : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void Button1_Click(object sender, EventArgs e)
        {//上传
            if (FileUpload1.HasFile)
            {
                //this.Label1.Text = FileUpload1.PostedFile.FileName;
               string filepath = FileUpload1.PostedFile.FileName;
                string filename = filepath.Substring(filepath.LastIndexOf("\\") + 1);
                string fileindex = filename.Substring(filename.LastIndexOf(".") + 1);

                if (fileindex == "gif")
                {
                   //this.Label1.Text= Server.MapPath("Prod_Images");
                   string newfilepath= Server.MapPath("Prod_Images\\")+filename;
                    FileUpload1.PostedFile.SaveAs(newfilepath);
                    Image1.ImageUrl = "Prod_Images//" + filename;
                }
                else
                {
                    this.Label1.Text = "必须是.gif";
                }

            }
            else
            {

            }
        }

        protected void Button2_Click(object sender, EventArgs e)
        {//添加商品

            MyPetShopEntities db = new MyPetShopEntities();
            Product p = new Product();
            p.CategoryId = int.Parse(ddl_cate.SelectedValue);
            p.SuppId = int.Parse(ddl_supp.SelectedValue);
            p.ListPrice =Convert.ToDecimal( txt_list.Text.Trim());
            p.UnitCost = Convert.ToDecimal(txt_unit.Text.Trim());
            p.Name = txt_name.Text.Trim();
            p.Descn = txt_desp.Text.Trim();
            p.Qty = Convert.ToInt16(txt_qty.Text);
            p.Image = Image1.ImageUrl;

            db.Product.Add(p);
            db.SaveChanges();

        }
    }
}