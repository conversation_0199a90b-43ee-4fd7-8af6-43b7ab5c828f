﻿<%@ Page Title="" Language="C#" MasterPageFile="~/h.Master" AutoEventWireup="true" CodeBehind="productAdd.aspx.cs" Inherits="WebApplication1.productAdd" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <p>
    商品添加</p>
 <table class="auto-style2">
    <tr>
        <td style="width: 192px">
            <p>
                商品添加</p>
            <p>
                分类 ：<asp:DropDownList ID="ddl_cate" runat="server" DataSourceID="LinqDataSource1" DataTextField="Name" DataValueField="CategoryId">
                </asp:DropDownList>
                <asp:LinqDataSource ID="LinqDataSource1" runat="server" ContextTypeName="WebApplication1.MyPetShopEntities" EntityTypeName="" Select="new (CategoryId, Name)" TableName="Category">
                </asp:LinqDataSource>
            </p>
            <p>
                成本价格：<asp:TextBox ID="txt_unit" runat="server" Width="61px">1</asp:TextBox>
            </p>
            <p>
                售价：<asp:TextBox ID="txt_list" runat="server" Width="60px">1</asp:TextBox>
            </p>
            <p>
                供应商：<asp:DropDownList ID="ddl_supp" runat="server" DataSourceID="LinqDataSource2" DataTextField="Name" DataValueField="SuppId">
                </asp:DropDownList>
                <asp:LinqDataSource ID="LinqDataSource2" runat="server" ContextTypeName="WebApplication1.MyPetShopEntities" EntityTypeName="" Select="new (SuppId, Name)" TableName="Supplier">
                </asp:LinqDataSource>
            </p>
            <p>
                名称：<asp:TextBox ID="txt_name" runat="server">a1</asp:TextBox>
            </p>
            <p>
                描述：<asp:TextBox ID="txt_desp" runat="server">aaaa</asp:TextBox>
            </p>
            <p>
                数量：<asp:TextBox ID="txt_qty" runat="server">10</asp:TextBox>
            </p>
        </td>
        <td style="width: 337px">图片：<br />
            <asp:Image ID="Image1" runat="server" Height="86px" Width="122px" />
            <br />
            <br />
            <br />
            <asp:FileUpload ID="FileUpload1" runat="server" />
            <br />
            <br />
            <asp:Button ID="Button1" runat="server" Text="上传" CausesValidation="False" OnClick="Button1_Click" />
        </td>
    </tr>
</table>
<p>
    <asp:Button ID="Button2" runat="server" Text="添加" OnClick="Button2_Click" style="height: 21px" />
    <asp:Label ID="Label2" runat="server" Text="Label"></asp:Label>
</p>





</asp:Content>
