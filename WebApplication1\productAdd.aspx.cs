﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebApplication1
{
    public partial class productAdd : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void Button1_Click(object sender, EventArgs e)
        {//上传


            if (FileUpload1.HasFile) { 

               string filepath=FileUpload1.PostedFile.FileName;
              string fileindex= filepath.Substring(   filepath.LastIndexOf(".")+1);
                string filename=filepath.Substring(filepath.LastIndexOf("\\")+1);

                if(fileindex=="gif")
                {
                    //request\response\session\server

                  string newfilename=Server.MapPath("Prod_Images\\")+filename;
                    FileUpload1.PostedFile.SaveAs(newfilename);
                    Image1.ImageUrl = "Prod_Images\\"+filename;
                }
            
            
            }

        }

        protected void Button2_Click(object sender, EventArgs e)
        {//添加功能
            Product p = new Product();
            p.CategoryId = int.Parse(this.ddl_cate.SelectedItem.Value);
            p.SuppId = int.Parse(this.ddl_supp.SelectedItem.Value);
            p.ListPrice = Convert.ToDecimal(this.txt_list.Text.Trim());
            p.UnitCost = Convert.ToDecimal(this.txt_unit.Text.Trim());
            p.Name = txt_name.Text.Trim();
            p.Descn = txt_desp.Text.Trim();
            p.Qty = Convert.ToInt16(txt_qty.Text);

            p.Image = this.Image1.ImageUrl;

            db.Product.Add(p);
            db.SaveChanges();

            Response.Redirect("productList.aspx");

        }
    }
}