﻿<?xml version="1.0" encoding="utf-8"?>
<Mapping Space="C-S" xmlns="http://schemas.microsoft.com/ado/2009/11/mapping/cs">
  <EntityContainerMapping StorageEntityContainer="MyPetShopModelStoreContainer" CdmEntityContainer="MyPetShopEntities">
    <EntitySetMapping Name="CartItem">
      <EntityTypeMapping TypeName="MyPetShopModel.CartItem">
        <MappingFragment StoreEntitySet="CartItem">
          <ScalarProperty Name="CartItemId" ColumnName="CartItemId" />
          <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
          <ScalarProperty Name="ProId" ColumnName="ProId" />
          <ScalarProperty Name="ProName" ColumnName="ProName" />
          <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
          <ScalarProperty Name="Qty" ColumnName="Qty" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Category">
      <EntityTypeMapping TypeName="MyPetShopModel.Category">
        <MappingFragment StoreEntitySet="Category">
          <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="Descn" ColumnName="Descn" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Customer">
      <EntityTypeMapping TypeName="MyPetShopModel.Customer">
        <MappingFragment StoreEntitySet="Customer">
          <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="Password" ColumnName="Password" />
          <ScalarProperty Name="Email" ColumnName="Email" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Order">
      <EntityTypeMapping TypeName="MyPetShopModel.Order">
        <MappingFragment StoreEntitySet="Order">
          <ScalarProperty Name="OrderId" ColumnName="OrderId" />
          <ScalarProperty Name="CustomerId" ColumnName="CustomerId" />
          <ScalarProperty Name="UserName" ColumnName="UserName" />
          <ScalarProperty Name="OrderDate" ColumnName="OrderDate" />
          <ScalarProperty Name="Addr1" ColumnName="Addr1" />
          <ScalarProperty Name="Addr2" ColumnName="Addr2" />
          <ScalarProperty Name="City" ColumnName="City" />
          <ScalarProperty Name="State" ColumnName="State" />
          <ScalarProperty Name="Zip" ColumnName="Zip" />
          <ScalarProperty Name="Phone" ColumnName="Phone" />
          <ScalarProperty Name="Status" ColumnName="Status" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="OrderItem">
      <EntityTypeMapping TypeName="MyPetShopModel.OrderItem">
        <MappingFragment StoreEntitySet="OrderItem">
          <ScalarProperty Name="ItemId" ColumnName="ItemId" />
          <ScalarProperty Name="OrderId" ColumnName="OrderId" />
          <ScalarProperty Name="ProName" ColumnName="ProName" />
          <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
          <ScalarProperty Name="Qty" ColumnName="Qty" />
          <ScalarProperty Name="TotalPrice" ColumnName="TotalPrice" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Product">
      <EntityTypeMapping TypeName="MyPetShopModel.Product">
        <MappingFragment StoreEntitySet="Product">
          <ScalarProperty Name="ProductId" ColumnName="ProductId" />
          <ScalarProperty Name="CategoryId" ColumnName="CategoryId" />
          <ScalarProperty Name="ListPrice" ColumnName="ListPrice" />
          <ScalarProperty Name="UnitCost" ColumnName="UnitCost" />
          <ScalarProperty Name="SuppId" ColumnName="SuppId" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="Descn" ColumnName="Descn" />
          <ScalarProperty Name="Image" ColumnName="Image" />
          <ScalarProperty Name="Qty" ColumnName="Qty" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
    <EntitySetMapping Name="Supplier">
      <EntityTypeMapping TypeName="MyPetShopModel.Supplier">
        <MappingFragment StoreEntitySet="Supplier">
          <ScalarProperty Name="SuppId" ColumnName="SuppId" />
          <ScalarProperty Name="Name" ColumnName="Name" />
          <ScalarProperty Name="Addr1" ColumnName="Addr1" />
          <ScalarProperty Name="Addr2" ColumnName="Addr2" />
          <ScalarProperty Name="City" ColumnName="City" />
          <ScalarProperty Name="State" ColumnName="State" />
          <ScalarProperty Name="Zip" ColumnName="Zip" />
          <ScalarProperty Name="Phone" ColumnName="Phone" />
        </MappingFragment>
      </EntityTypeMapping>
    </EntitySetMapping>
  </EntityContainerMapping>
</Mapping>