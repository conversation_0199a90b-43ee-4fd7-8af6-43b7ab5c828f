﻿<%@ Page Title="" Language="C#" MasterPageFile="~/h.Master" AutoEventWireup="true" CodeBehind="productList.aspx.cs" Inherits="Web1.productList" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:GridView ID="GridView1" runat="server" AutoGenerateColumns="False" OnRowDeleting="GridView1_RowDeleting" OnRowEditing="GridView1_RowEditing">
    <Columns>
        <asp:BoundField DataField="ProductId" HeaderText="商品编号" />
        <asp:BoundField DataField="Name" HeaderText="名称" />
        <asp:BoundField DataField="ListPrice" HeaderText="价格" />
        <asp:ImageField DataImageUrlField="Image" HeaderText="图片">
            <ControlStyle Height="50px" Width="50px" />
        </asp:ImageField>
        <asp:CommandField ShowEditButton="True" />
        <asp:CommandField ShowDeleteButton="True" />
    </Columns>
</asp:GridView>
</asp:Content>
