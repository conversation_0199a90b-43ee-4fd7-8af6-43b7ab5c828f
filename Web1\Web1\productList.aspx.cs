﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productList : System.Web.UI.Page
    {
        
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            
            databind();
        }

        public void databind()
        {
            
            this.GridView1.DataSource= db.Product.ToList();
            this.GridView1.DataBind();
        }

        protected void GridView1_RowDeleting(object sender, GridViewDeleteEventArgs e)
        {
           string pid=this.GridView1.Rows[ e.RowIndex].Cells[0].Text;
           Product p= db.Product.Find(int.Parse(pid));

            db.Product.Remove(p);
            db.SaveChanges();
            databind();
        }

        protected void GridView1_RowEditing(object sender, GridViewEditEventArgs e)
        {
            //e.NewEditIndex;

            string pid = this.GridView1.Rows[e.NewEditIndex].Cells[0].Text;
            Response.Redirect("productEdit.aspx?pid="+pid);
        }
    }
}