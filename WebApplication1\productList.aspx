﻿<%@ Page Title="" Language="C#" MasterPageFile="~/h.Master" AutoEventWireup="true" CodeBehind="productList.aspx.cs" Inherits="WebApplication1.productList" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">
    <asp:GridView ID="GridView2" runat="server" AutoGenerateColumns="False" OnRowDeleting="GridView2_RowDeleting" OnRowEditing="GridView2_RowEditing" PageSize="4" Height="105px" Width="420px" AllowPaging="True" OnPageIndexChanging="GridView2_PageIndexChanging">
    <Columns>
        <asp:BoundField DataField="ProductId" HeaderText="商品ID" />
        <asp:BoundField DataField="Name" HeaderText="名称" />
        <asp:BoundField DataField="Listprice" HeaderText="单价" />
        <asp:ImageField DataImageUrlField="Image" HeaderText="图片">
            <ControlStyle Height="50px" Width="50px" />
        </asp:ImageField>
        <asp:CommandField ShowEditButton="True" />
        <asp:CommandField ShowDeleteButton="True" />
    </Columns>
</asp:GridView>
<p>
    <asp:Button ID="Button1" runat="server" OnClick="Button1_Click" Text="Button" />
</p>
<p>
    &nbsp;</p>
</asp:Content>
