﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="h.master.cs" Inherits="Web1.h" %>
<!DOCTYPE html>

<html>
<head runat="server">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title></title>

    <style type="text/css">
        .auto-style1 {
            width: 1000px;
        }
        .auto-style2 {
            width: 100%;
            border-bottom-color:bisque;
        }
        .auto-style3 {
            width: 78px;
        }
        .auto-style7 {
            background-color:aliceblue;
            
        }
        .auto-left {
            background-color:cornsilk;
            width: 200px;
            height: 400px;
        }
        .auto-right {
            
            width: 800px;
            height: 400px;
        }
        .auto-style8 {
            width: 100%;
            background-color: #CCFFFF;
        }
        .auto-style11 {
            height: 20px;
        }
        .auto-style12 {
            width: 93px;
            height: 38px;
        }
    </style>

</head>
<body>
    <form id="form1" runat="server">
        <div>
   
            <table cellpadding="0" cellspacing="0" class="auto-style1">
                <tr>
                    <td colspan="2">
                        <table cellpadding="0" cellspacing="0" class="auto-style2">
                            <tr>
                                <td class="auto-style3">
                                    <img alt="" class="auto-style12" src="Images/logo.gif" /></td>
                                <td>
                                    后台信息管理</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <asp:Label ID="Label1" runat="server" Text="Label"></asp:Label>
                    </td>
                </tr>
                <tr>
                    <td class="auto-left" valign="top"><br />
                        <br />
                        <asp:TreeView ID="TreeView1" runat="server">
                            <Nodes>
                                <asp:TreeNode NavigateUrl="~/index_m.aspx" Text="系统管理" Value="系统管理">
                                    <asp:TreeNode Text="用户信息管理" Value="用户信息管理">
                                        <asp:TreeNode Text="用户列表" Value="用户列表"></asp:TreeNode>
                                        <asp:TreeNode Text="新增用户" Value="新增用户"></asp:TreeNode>
                                    </asp:TreeNode>
                                    <asp:TreeNode Text="商品管理" Value="商品管理">
                                        <asp:TreeNode Text="商品列表" Value="商品列表" NavigateUrl="~/productList.aspx"></asp:TreeNode>
                                        <asp:TreeNode Text="新增商品" Value="新增商品" NavigateUrl="~/productAdd.aspx"></asp:TreeNode>
                                    </asp:TreeNode>
                                </asp:TreeNode>
                            </Nodes>
                        </asp:TreeView>
                        <br />
                    </td>
                    <td class="auto-right" valign="top">
                        <table cellpadding="0" cellspacing="0" class="auto-style8">
                            <tr>
                                <td class="auto-style11">
                                    &nbsp;</td>
                            </tr>

                        </table>
                        <br />
                        <asp:ContentPlaceHolder ID="ContentPlaceHolder1" runat="server">
                        </asp:ContentPlaceHolder>
                    </td>
                </tr>
                <tr>
                    <td class="auto-style7">&nbsp;</td>
                    <td class="auto-style7">Copyright 2025</td>
                </tr>
            </table>
   
        </div>
    </form>
</body>
</html>
