﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class login : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {

        }

        protected void Button1_Click(object sender, EventArgs e)
        {
            string id = this.txt_id.Text.Trim();
            string pwd = this.txt_pwd.Text.Trim();

            MyPetShopEntities db = new MyPetShopEntities();
           Customer c= db.Customer.Find(int.Parse(id));

            if (c != null)
            {
                if (c.Password == pwd)
                {
                    Session["id"] = id;
                    Session["name"] = c.Name;
                    Response.Redirect("index_m.aspx");
                }
                else
                {
                    this.Label1.Text = "密码错误！";
                }
            }
            else
            {
                this.Label1.Text = "你不是管理 员！";
            }
        }
    }
}