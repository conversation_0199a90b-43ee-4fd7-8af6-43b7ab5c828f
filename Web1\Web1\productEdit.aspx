﻿<%@ Page Title="" Language="C#" MasterPageFile="~/h.Master" AutoEventWireup="true" CodeBehind="productEdit.aspx.cs" Inherits="Web1.productEdit" %>
<asp:Content ID="Content1" ContentPlaceHolderID="ContentPlaceHolder1" runat="server">

    <table class="auto-style2">
    <tr>
        <td style="width: 233px">
            <p>
                商品修改</p>
            <p>
                ID:<asp:TextBox ID="TextBox1" runat="server" ReadOnly="True" Width="58px"></asp:TextBox>
            </p>
            <p>
                分类 ：<asp:DropDownList ID="ddl_cate" runat="server" DataSourceID="LinqDataSource1" DataTextField="Name" DataValueField="CategoryId">
                </asp:DropDownList>
                <asp:LinqDataSource ID="LinqDataSource1" runat="server" ContextTypeName="Web1.MyPetShopEntities" EntityTypeName="" Select="new (CategoryId, Name)" TableName="Category">
                </asp:LinqDataSource>
            </p>
            <p>
                价格1：<asp:TextBox ID="txt_unit" runat="server" Width="61px">0</asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator2" runat="server" ControlToValidate="txt_unit" ErrorMessage="*" ForeColor="Red"></asp:RequiredFieldValidator>
                <asp:CompareValidator ID="CompareValidator1" runat="server" ControlToValidate="txt_unit" ErrorMessage="不是数字！" Operator="DataTypeCheck" Type="Double" style="color: #FF0066"></asp:CompareValidator>
            </p>
            <p>
                价格2：<asp:TextBox ID="txt_list" runat="server" Width="60px">0</asp:TextBox>
            </p>
            <p>
                供应商：<asp:DropDownList ID="ddl_supp" runat="server" DataSourceID="LinqDataSource2" DataTextField="Name" DataValueField="SuppId">
                </asp:DropDownList>
                <asp:LinqDataSource ID="LinqDataSource2" runat="server" ContextTypeName="Web1.MyPetShopEntities" EntityTypeName="" Select="new (SuppId, Name)" TableName="Supplier">
                </asp:LinqDataSource>
            </p>
            <p>
                名称：<asp:TextBox ID="txt_name" runat="server">a1</asp:TextBox>
                <asp:RequiredFieldValidator ID="RequiredFieldValidator1" runat="server" ControlToValidate="txt_name" ErrorMessage="*" style="color: #FF0066"></asp:RequiredFieldValidator>
            </p>
            <p>
                描述：<asp:TextBox ID="txt_desp" runat="server">aaaa</asp:TextBox>
            </p>
            <p>
                数量：<asp:TextBox ID="txt_qty" runat="server">10</asp:TextBox>
            </p>
        </td>
        <td>图片：<br />
            <asp:Image ID="Image1" runat="server" Height="86px" Width="122px" />
            <br />
            <br />
            <br />
            <asp:FileUpload ID="FileUpload1" runat="server" />
            <br />
            <br />
            <asp:Button ID="Button1" runat="server" Text="上传" CausesValidation="False" OnClick="Button1_Click" style="height: 21px" />
        </td>
    </tr>
</table>

    <asp:Button ID="Button2" runat="server" Text="修改" OnClick="Button2_Click" />


<asp:Label ID="Label2" runat="server" Text="Label"></asp:Label>


</asp:Content>
