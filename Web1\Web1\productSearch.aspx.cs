﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Web1
{
    public partial class productSearch : System.Web.UI.Page
    {
        MyPetShopEntities db = new MyPetShopEntities();
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                string pn = Request.QueryString["pn"].ToString();
                
                var re = db.Product.Where(p => p.Name.Contains(pn));

                this.DataList2.DataSource = re.ToList();
                this.DataList2.DataBind();
            }
        }
    }
}